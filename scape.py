#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频元数据刮削模块
从多个源获取元数据并生成NFO文件
支持 hanime1.me, themoviedb.org, getchu.com
"""

import os
import re
import yaml
import logging
import requests
import time
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from xml.dom import minidom
from datetime import datetime
import json

# 导入视频信息类
from craw import VideoInfo


class VideoScraper:
    """视频元数据刮削器"""

    def __init__(self, config_path: str = "config.yml"):
        """初始化刮削器"""
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        self.session = requests.Session()
        self._setup_session()
        self.download_dir = Path(self.config.get('download', {}).get('download_dir', './downloads'))

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'scraping': {
                'sources': {
                    'hanime1': {'enabled': True, 'base_url': 'https://hanime1.me', 'priority': 1},
                    'themoviedb': {'enabled': True, 'base_url': 'https://api.themoviedb.org/3', 'priority': 2},
                    'getchu': {'enabled': True, 'base_url': 'https://www.getchu.com', 'priority': 3}
                },
                'nfo': {
                    'encoding': 'utf-8',
                    'field_mapping': {
                        'title': 'title',
                        'originaltitle': 'originaltitle',
                        'plot': 'plot',
                        'year': 'year',
                        'studio': 'studio',
                        'genre': 'genre'
                    }
                },
                'images': {
                    'download_poster': True,
                    'download_fanart': True,
                    'download_thumb': True
                },
                'settings': {
                    'delay_between_requests': 1,
                    'timeout': 30,
                    'retry_times': 3,
                    'skip_existing_nfo': True
                }
            },
            'download': {
                'download_dir': './downloads'
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }

    def _setup_session(self):
        """设置请求会话"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('VideoScraper')

        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        logger.setLevel(level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s'))
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _make_request(self, url: str, params: Dict = None, headers: Dict = None) -> Optional[requests.Response]:
        """发送HTTP请求"""
        settings = self.config.get('scraping', {}).get('settings', {})
        timeout = settings.get('timeout', 30)
        retry_times = settings.get('retry_times', 3)
        delay = settings.get('delay_between_requests', 1)

        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)

        for attempt in range(retry_times):
            try:
                self.logger.debug(f"请求URL: {url}, 尝试: {attempt + 1}")
                response = self.session.get(url, params=params, headers=request_headers, timeout=timeout)
                response.raise_for_status()

                if delay > 0:
                    time.sleep(delay)

                return response

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{retry_times}): {e}")
                if attempt < retry_times - 1:
                    time.sleep(2 ** attempt)
                else:
                    self.logger.error(f"请求最终失败: {url}")
                    return None

    def scrape_hanime1_metadata(self, video_info: VideoInfo) -> Dict[str, Any]:
        """从 hanime1.me 刮削元数据"""
        metadata = {}

        if not video_info.url:
            self.logger.warning("没有 hanime1.me URL")
            return metadata

        self.logger.info(f"从 hanime1.me 刮削元数据: {video_info.title}")

        response = self._make_request(video_info.url)
        if not response:
            return metadata

        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取标题信息
            title_elem = soup.find('h1', class_='video-title') or soup.find('h1')
            if title_elem:
                metadata['title'] = title_elem.get_text(strip=True)

            # 提取日文标题
            jp_title_elem = soup.find('span', class_='jp-title') or soup.find('h2', class_='jp-title')
            if jp_title_elem:
                metadata['title_jp'] = jp_title_elem.get_text(strip=True)

            # 提取描述
            desc_elem = soup.find('div', class_='description') or soup.find('div', class_='synopsis')
            if desc_elem:
                metadata['plot'] = desc_elem.get_text(strip=True)

            # 提取发布日期
            date_elem = soup.find('span', class_='release-date') or soup.find('time')
            if date_elem:
                date_text = date_elem.get_text(strip=True)
                # 解析日期
                date_match = re.search(r'(\d{4})-(\d{1,2})-(\d{1,2})', date_text)
                if date_match:
                    metadata['premiered'] = f"{date_match.group(1)}-{date_match.group(2):0>2}-{date_match.group(3):0>2}"
                    metadata['year'] = date_match.group(1)

            # 提取制作商
            studio_elem = soup.find('span', class_='studio') or soup.find('div', class_='brand')
            if studio_elem:
                metadata['studio'] = studio_elem.get_text(strip=True)
                metadata['maker'] = metadata['studio']

            # 提取标签/类型
            tag_elems = soup.find_all('a', class_='tag') or soup.find_all('span', class_='genre')
            if tag_elems:
                tags = [tag.get_text(strip=True) for tag in tag_elems]
                metadata['genre'] = tags
                metadata['tag'] = tags

            # 提取评分
            rating_elem = soup.find('span', class_='rating') or soup.find('div', class_='rating')
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    metadata['rating'] = float(rating_match.group(1))

            # 提取时长
            duration_elem = soup.find('span', class_='duration') or soup.find('div', class_='duration')
            if duration_elem:
                duration_text = duration_elem.get_text(strip=True)
                # 转换为分钟
                duration_match = re.search(r'(\d+)', duration_text)
                if duration_match:
                    metadata['runtime'] = int(duration_match.group(1))

            # 提取图片
            poster_elem = soup.find('img', class_='poster') or soup.find('img', class_='cover')
            if poster_elem:
                poster_url = poster_elem.get('src') or poster_elem.get('data-src')
                if poster_url:
                    if not poster_url.startswith('http'):
                        base_url = self.config.get('scraping', {}).get('sources', {}).get('hanime1', {}).get('base_url', 'https://hanime1.me')
                        poster_url = urljoin(base_url, poster_url)
                    metadata['poster_url'] = poster_url

            # 设置其他默认值
            metadata['customrating'] = '里番'
            metadata['mpaa'] = '里番'
            metadata['hanime1'] = video_info.url

        except Exception as e:
            self.logger.error(f"解析 hanime1.me 页面失败: {e}")

        return metadata

    def scrape_themoviedb_metadata(self, video_info: VideoInfo) -> Dict[str, Any]:
        """从 TheMovieDB 刮削元数据"""
        metadata = {}

        sources = self.config.get('scraping', {}).get('sources', {})
        tmdb_config = sources.get('themoviedb', {})

        if not tmdb_config.get('enabled', True):
            return metadata

        api_key = tmdb_config.get('api_key', '')
        if not api_key:
            self.logger.warning("TheMovieDB API密钥未配置")
            return metadata

        base_url = tmdb_config.get('base_url', 'https://api.themoviedb.org/3')

        # 搜索视频
        search_url = f"{base_url}/search/movie"
        params = {
            'api_key': api_key,
            'query': video_info.title,
            'language': 'zh-CN'
        }

        self.logger.info(f"从 TheMovieDB 搜索: {video_info.title}")

        response = self._make_request(search_url, params=params)
        if not response:
            return metadata

        try:
            data = response.json()
            results = data.get('results', [])

            if not results:
                self.logger.info("TheMovieDB 未找到匹配结果")
                return metadata

            # 选择第一个结果
            movie = results[0]
            movie_id = movie.get('id')

            # 获取详细信息
            detail_url = f"{base_url}/movie/{movie_id}"
            detail_params = {
                'api_key': api_key,
                'language': 'zh-CN',
                'append_to_response': 'credits,images'
            }

            detail_response = self._make_request(detail_url, params=detail_params)
            if detail_response:
                detail_data = detail_response.json()

                # 提取元数据
                metadata['title'] = detail_data.get('title', '')
                metadata['originaltitle'] = detail_data.get('original_title', '')
                metadata['plot'] = detail_data.get('overview', '')
                metadata['year'] = detail_data.get('release_date', '')[:4] if detail_data.get('release_date') else ''
                metadata['premiered'] = detail_data.get('release_date', '')
                metadata['rating'] = detail_data.get('vote_average', 0)
                metadata['runtime'] = detail_data.get('runtime', 0)

                # 提取类型
                genres = detail_data.get('genres', [])
                if genres:
                    metadata['genre'] = [g['name'] for g in genres]

                # 提取制作公司
                companies = detail_data.get('production_companies', [])
                if companies:
                    metadata['studio'] = companies[0]['name']

                # 提取图片
                if detail_data.get('poster_path'):
                    metadata['poster_url'] = f"https://image.tmdb.org/t/p/original{detail_data['poster_path']}"

                if detail_data.get('backdrop_path'):
                    metadata['fanart_url'] = f"https://image.tmdb.org/t/p/original{detail_data['backdrop_path']}"

        except Exception as e:
            self.logger.error(f"解析 TheMovieDB 数据失败: {e}")

        return metadata

    def scrape_getchu_metadata(self, video_info: VideoInfo) -> Dict[str, Any]:
        """从 Getchu 刮削元数据"""
        metadata = {}

        sources = self.config.get('scraping', {}).get('sources', {})
        getchu_config = sources.get('getchu', {})

        if not getchu_config.get('enabled', True):
            return metadata

        # Getchu 搜索比较复杂，这里提供基础框架
        # 实际实现需要根据网站结构调整

        self.logger.info(f"从 Getchu 搜索: {video_info.title}")

        try:
            # 构建搜索URL
            base_url = getchu_config.get('base_url', 'https://www.getchu.com')
            search_url = f"{base_url}/php/search.phtml"

            params = {
                'search_keyword': video_info.title,
                'genre': 'pc_soft'
            }

            response = self._make_request(search_url, params=params)
            if not response:
                return metadata

            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找搜索结果
            result_links = soup.find_all('a', href=re.compile(r'/soft\.phtml\?id='))

            if result_links:
                # 获取第一个结果的详细页面
                detail_url = urljoin(base_url, result_links[0]['href'])
                detail_response = self._make_request(detail_url)

                if detail_response:
                    detail_soup = BeautifulSoup(detail_response.text, 'html.parser')

                    # 提取标题
                    title_elem = detail_soup.find('h1') or detail_soup.find('title')
                    if title_elem:
                        metadata['title'] = title_elem.get_text(strip=True)

                    # 提取发布日期
                    date_elem = detail_soup.find('td', string=re.compile(r'発売日'))
                    if date_elem and date_elem.find_next_sibling('td'):
                        date_text = date_elem.find_next_sibling('td').get_text(strip=True)
                        date_match = re.search(r'(\d{4})/(\d{1,2})/(\d{1,2})', date_text)
                        if date_match:
                            metadata['premiered'] = f"{date_match.group(1)}-{date_match.group(2):0>2}-{date_match.group(3):0>2}"
                            metadata['year'] = date_match.group(1)

                    # 提取制作商
                    brand_elem = detail_soup.find('td', string=re.compile(r'ブランド'))
                    if brand_elem and brand_elem.find_next_sibling('td'):
                        brand_text = brand_elem.find_next_sibling('td').get_text(strip=True)
                        metadata['studio'] = brand_text
                        metadata['maker'] = brand_text

                    # 提取图片
                    img_elem = detail_soup.find('img', src=re.compile(r'/gc/'))
                    if img_elem:
                        img_url = img_elem.get('src')
                        if img_url and not img_url.startswith('http'):
                            img_url = urljoin(base_url, img_url)
                        metadata['poster_url'] = img_url

        except Exception as e:
            self.logger.error(f"从 Getchu 刮削失败: {e}")

        return metadata

    def merge_metadata(self, *metadata_dicts: Dict[str, Any]) -> Dict[str, Any]:
        """合并多个元数据源的数据"""
        merged = {}

        # 按优先级合并数据
        for metadata in metadata_dicts:
            for key, value in metadata.items():
                if value and (key not in merged or not merged[key]):
                    merged[key] = value

        return merged

    def scrape_all_sources(self, video_info: VideoInfo) -> Dict[str, Any]:
        """从所有启用的源刮削元数据"""
        sources = self.config.get('scraping', {}).get('sources', {})
        all_metadata = []

        # 按优先级排序源
        sorted_sources = sorted(sources.items(), key=lambda x: x[1].get('priority', 999))

        for source_name, source_config in sorted_sources:
            if not source_config.get('enabled', True):
                continue

            self.logger.info(f"从 {source_name} 刮削元数据...")

            try:
                if source_name == 'hanime1':
                    metadata = self.scrape_hanime1_metadata(video_info)
                elif source_name == 'themoviedb':
                    metadata = self.scrape_themoviedb_metadata(video_info)
                elif source_name == 'getchu':
                    metadata = self.scrape_getchu_metadata(video_info)
                else:
                    continue

                if metadata:
                    all_metadata.append(metadata)
                    self.logger.info(f"从 {source_name} 获取到 {len(metadata)} 个字段")

            except Exception as e:
                self.logger.error(f"从 {source_name} 刮削失败: {e}")

        # 合并所有元数据
        merged_metadata = self.merge_metadata(*all_metadata)

        # 添加基础信息
        merged_metadata.update({
            'dateadded': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'lockdata': 'false',
            'uncensored': 'False'
        })

        return merged_metadata

    def generate_nfo_xml(self, metadata: Dict[str, Any]) -> str:
        """生成NFO XML内容"""
        # 创建根元素
        root = ET.Element('movie')

        # 字段映射
        field_mapping = self.config.get('scraping', {}).get('nfo', {}).get('field_mapping', {})

        # 基础字段
        basic_fields = [
            'title', 'originaltitle', 'plot', 'outline', 'year', 'premiered',
            'releasedate', 'runtime', 'rating', 'criticrating', 'tagline',
            'customrating', 'mpaa', 'lockdata', 'dateadded', 'sorttitle',
            'studio', 'maker', 'num', 'release', 'website', 'hanime1',
            'uncensored', 'title_cn', 'title_jp', 'title_rm'
        ]

        # 添加基础字段
        for field in basic_fields:
            value = metadata.get(field, '')
            if value:
                elem = ET.SubElement(root, field)
                if isinstance(value, (int, float)):
                    elem.text = str(value)
                else:
                    elem.text = str(value)

        # 添加标签和类型
        tags = metadata.get('tag', []) or metadata.get('genre', [])
        if isinstance(tags, str):
            tags = [tags]

        for tag in tags:
            tag_elem = ET.SubElement(root, 'tag')
            tag_elem.text = str(tag)

        genres = metadata.get('genre', [])
        if isinstance(genres, str):
            genres = [genres]

        for genre in genres:
            genre_elem = ET.SubElement(root, 'genre')
            genre_elem.text = str(genre)

        # 添加系列信息
        if metadata.get('series'):
            set_elem = ET.SubElement(root, 'set')
            name_elem = ET.SubElement(set_elem, 'name')
            name_elem.text = str(metadata['series'])

        # 添加文件信息（如果有）
        if metadata.get('fileinfo'):
            fileinfo_elem = ET.SubElement(root, 'fileinfo')
            # 这里可以添加更详细的文件信息

        # 添加图片元素
        for img_type in ['poster', 'thumb', 'fanart', 'cover']:
            img_elem = ET.SubElement(root, img_type)
            img_elem.text = ''  # 图片路径将在下载后填入

        # 格式化XML
        rough_string = ET.tostring(root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)

        # 添加XML声明和格式化
        xml_str = reparsed.toprettyxml(indent='  ', encoding=None)

        # 清理多余的空行
        lines = [line for line in xml_str.split('\n') if line.strip()]

        # 添加UTF-8声明
        if not xml_str.startswith('<?xml'):
            lines.insert(0, '<?xml version="1.0" encoding="utf-8" standalone="yes"?>')

        return '\n'.join(lines)

    def download_images(self, metadata: Dict[str, Any], video_filename: str) -> Dict[str, str]:
        """下载相关图片"""
        image_config = self.config.get('scraping', {}).get('images', {})
        downloaded_images = {}

        if not any(image_config.values()):
            return downloaded_images

        base_filename = Path(video_filename).stem

        # 图片类型映射
        image_types = {
            'poster': 'poster_url',
            'fanart': 'fanart_url',
            'thumb': 'thumb_url'
        }

        for img_type, url_key in image_types.items():
            if not image_config.get(f'download_{img_type}', True):
                continue

            img_url = metadata.get(url_key)
            if not img_url:
                continue

            try:
                # 确定文件扩展名
                parsed_url = urlparse(img_url)
                ext = os.path.splitext(parsed_url.path)[1] or '.jpg'

                # 生成文件名
                if img_type == 'poster':
                    img_filename = f"{base_filename}-poster{ext}"
                elif img_type == 'fanart':
                    img_filename = f"{base_filename}-fanart{ext}"
                elif img_type == 'thumb':
                    img_filename = f"{base_filename}-thumb{ext}"
                else:
                    img_filename = f"{base_filename}-{img_type}{ext}"

                img_path = self.download_dir / img_filename

                # 下载图片
                if self._download_image(img_url, img_path):
                    downloaded_images[img_type] = img_filename
                    self.logger.info(f"下载图片成功: {img_filename}")
                else:
                    self.logger.warning(f"下载图片失败: {img_url}")

            except Exception as e:
                self.logger.error(f"下载图片异常: {img_url} - {e}")

        return downloaded_images

    def _download_image(self, url: str, file_path: Path) -> bool:
        """下载单个图片"""
        if file_path.exists():
            self.logger.debug(f"图片已存在: {file_path.name}")
            return True

        try:
            response = self._make_request(url)
            if not response:
                return False

            with open(file_path, 'wb') as f:
                f.write(response.content)

            return True

        except Exception as e:
            self.logger.error(f"下载图片失败: {e}")
            return False

    def save_nfo_file(self, metadata: Dict[str, Any], video_filename: str) -> Optional[Path]:
        """保存NFO文件"""
        base_filename = Path(video_filename).stem
        nfo_filename = f"{base_filename}.nfo"
        nfo_path = self.download_dir / nfo_filename

        # 检查是否跳过已存在的NFO
        settings = self.config.get('scraping', {}).get('settings', {})
        if settings.get('skip_existing_nfo', True) and nfo_path.exists():
            self.logger.info(f"NFO文件已存在，跳过: {nfo_filename}")
            return nfo_path

        try:
            # 下载图片
            downloaded_images = self.download_images(metadata, video_filename)

            # 更新元数据中的图片路径
            for img_type, img_filename in downloaded_images.items():
                metadata[img_type] = img_filename

            # 生成NFO内容
            nfo_content = self.generate_nfo_xml(metadata)

            # 保存文件
            encoding = self.config.get('scraping', {}).get('nfo', {}).get('encoding', 'utf-8')
            with open(nfo_path, 'w', encoding=encoding) as f:
                f.write(nfo_content)

            self.logger.info(f"NFO文件保存成功: {nfo_filename}")
            return nfo_path

        except Exception as e:
            self.logger.error(f"保存NFO文件失败: {e}")
            return None

    def scrape_and_save_nfo(self, video_info: VideoInfo) -> Optional[Path]:
        """完整的刮削和保存流程"""
        self.logger.info(f"开始刮削视频元数据: {video_info.title}")

        # 刮削所有源的元数据
        metadata = self.scrape_all_sources(video_info)

        if not metadata:
            self.logger.warning(f"未获取到元数据: {video_info.title}")
            return None

        # 确定视频文件名
        video_filename = self._find_video_filename(video_info)
        if not video_filename:
            # 使用默认文件名
            video_filename = f"{video_info.title}_{video_info.id}.mp4"

        # 保存NFO文件
        nfo_path = self.save_nfo_file(metadata, video_filename)

        if nfo_path:
            self.logger.info(f"刮削完成: {video_info.title}")
        else:
            self.logger.error(f"刮削失败: {video_info.title}")

        return nfo_path

    def _find_video_filename(self, video_info: VideoInfo) -> Optional[str]:
        """查找视频文件名"""
        # 支持的视频格式
        video_formats = ['.mp4', '.mkv', '.avi', '.mov', '.wmv']

        # 搜索模式
        search_patterns = [
            f"*{video_info.id}*",
            f"*{video_info.title[:20]}*"
        ]

        for pattern in search_patterns:
            for file_path in self.download_dir.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in video_formats:
                    return file_path.name

        return None


def main():
    """主函数，用于测试"""
    import argparse
    from craw import HanimeCrawler

    parser = argparse.ArgumentParser(description='视频元数据刮削工具')
    parser.add_argument('--config', '-c', default='config.yml', help='配置文件路径')
    parser.add_argument('--video-id', help='指定视频ID进行刮削')
    parser.add_argument('--search', '-s', help='搜索关键词')
    parser.add_argument('--source', choices=['hanime1', 'themoviedb', 'getchu', 'all'],
                       default='all', help='指定刮削源')
    parser.add_argument('--no-images', action='store_true', help='不下载图片')

    args = parser.parse_args()

    # 创建刮削器
    scraper = VideoScraper(args.config)

    if args.video_id:
        # 处理单个视频
        video_info = VideoInfo(id=args.video_id, title=f"Video {args.video_id}")

        if args.source == 'all':
            metadata = scraper.scrape_all_sources(video_info)
        elif args.source == 'hanime1':
            metadata = scraper.scrape_hanime1_metadata(video_info)
        elif args.source == 'themoviedb':
            metadata = scraper.scrape_themoviedb_metadata(video_info)
        elif args.source == 'getchu':
            metadata = scraper.scrape_getchu_metadata(video_info)

        if metadata:
            print(f"获取到元数据: {len(metadata)} 个字段")
            for key, value in metadata.items():
                print(f"  {key}: {value}")

            # 保存NFO
            nfo_path = scraper.save_nfo_file(metadata, f"{args.video_id}.mp4")
            if nfo_path:
                print(f"NFO文件已保存: {nfo_path}")
        else:
            print("未获取到元数据")

    elif args.search:
        # 搜索并刮削
        crawler = HanimeCrawler(args.config)
        videos = crawler.search_videos(query=args.search, max_pages=1)

        if videos:
            print(f"找到 {len(videos)} 个视频")

            for video in videos:
                print(f"\n处理视频: {video.title}")
                nfo_path = scraper.scrape_and_save_nfo(video)
                if nfo_path:
                    print(f"  NFO已保存: {nfo_path.name}")
                else:
                    print("  刮削失败")
        else:
            print("没有找到视频")

    else:
        print("请指定 --video-id 或 --search 参数")


if __name__ == '__main__':
    main()