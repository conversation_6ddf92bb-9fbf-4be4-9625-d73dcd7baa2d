# Hanime1.me 视频抓取和处理系统

一个功能完整的视频抓取、下载、重命名和元数据刮削系统，专门用于处理 hanime1.me 网站的内容。

## 功能特性

- 🔍 **智能抓取**: 从 hanime1.me 抓取视频列表，支持按年月日筛选
- 📥 **批量下载**: 支持多线程下载和断点续传
- 📝 **自动重命名**: 根据可自定义的模板自动重命名文件
- 🏷️ **元数据刮削**: 从多个源（hanime1.me, TheMovieDB, Getchu）获取元数据
- 📄 **NFO生成**: 自动生成标准的NFO文件
- 🖼️ **图片下载**: 自动下载封面、海报等图片
- ⚙️ **灵活配置**: 通过YAML配置文件自定义所有设置

## 系统架构

```
├── config.yml      # 主配置文件
├── main.py         # 主程序入口
├── craw.py         # 网站抓取模块
├── rename.py       # 文件重命名模块
├── scape.py        # 元数据刮削模块
└── requirements.txt # 依赖项列表
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置设置

编辑 `config.yml` 文件，根据需要调整以下设置：

- **搜索参数**: 设置默认的搜索类型、年份、月份等
- **下载设置**: 配置下载目录、并发数、超时时间等
- **重命名规则**: 自定义文件命名模板
- **刮削源**: 配置各个元数据源的优先级和API密钥

### 3. TheMovieDB API配置（可选）

如需使用TheMovieDB作为元数据源：

1. 访问 https://www.themoviedb.org/settings/api 申请API密钥
2. 在 `config.yml` 中设置 `scraping.sources.themoviedb.api_key`

## 使用方法

### 基本用法

```bash
# 搜索并处理2025年1月的视频
python main.py --year 2025 --month 1 --pages 2

# 搜索特定关键词
python main.py --query "关键词" --pages 1

# 只列出视频，不下载
python main.py --list-only --query "关键词"

# 跳过某些步骤
python main.py --no-download --query "关键词"  # 只刮削，不下载
python main.py --no-rename --query "关键词"    # 不重命名
python main.py --no-scrape --query "关键词"    # 不刮削元数据
```

### 单独使用各模块

#### 抓取模块
```bash
python craw.py --query "关键词" --year 2025 --month 1 --details
```

#### 重命名模块
```bash
python rename.py --search "关键词" --template "with_year" --preview
```

#### 刮削模块
```bash
python scape.py --search "关键词" --source "hanime1"
```

## 配置文件说明

### 重命名模板

支持以下变量：
- `{title}`: 视频标题
- `{title_jp}`: 日文标题
- `{studio}`: 制作商
- `{num}`: 视频编号
- `{year}`: 发布年份
- `{rating}`: 评分

示例模板：
```yaml
templates:
  default: "[{studio}] {title} [{num}]"
  with_year: "[{studio}] {title} ({year}) [{num}]"
  simple: "{title} [{num}]"
```

### 搜索筛选

```yaml
date_filter:
  enabled: true
  year: 2025     # 年份筛选
  month: 1       # 月份筛选
  day: 0         # 日期筛选（0表示不限制）
```

## 输出文件

系统会在下载目录中生成以下文件：

```
downloads/
├── [制作商] 视频标题 [编号].mp4     # 重命名后的视频文件
├── [制作商] 视频标题 [编号].nfo     # NFO元数据文件
├── [制作商] 视频标题 [编号]-poster.jpg  # 封面图片
└── [制作商] 视频标题 [编号]-fanart.jpg  # 背景图片
```

## NFO文件格式

生成的NFO文件包含以下信息：
- 标题（中文、日文、英文）
- 剧情简介
- 发布日期和年份
- 制作商信息
- 评分和时长
- 标签和类型
- 图片路径
- 技术信息

## 注意事项

1. **网络连接**: 确保网络连接稳定，建议配置代理（如需要）
2. **请求频率**: 系统已内置请求间隔，避免对服务器造成过大压力
3. **存储空间**: 确保有足够的磁盘空间存储下载的视频和图片
4. **法律合规**: 请确保使用符合当地法律法规

## 故障排除

### 常见问题

1. **导入错误**: 确保已安装所有依赖项
2. **网络超时**: 调整配置文件中的超时设置
3. **文件权限**: 确保程序有读写下载目录的权限
4. **编码问题**: 确保系统支持UTF-8编码

### 日志调试

启用详细日志：
```bash
python main.py --verbose --query "关键词"
```

或在配置文件中设置：
```yaml
logging:
  level: "DEBUG"
```

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
