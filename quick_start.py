#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本
提供简化的交互式界面
"""

import sys
import os
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("    Hanime1.me 视频抓取和处理系统")
    print("=" * 60)
    print()

def check_dependencies():
    """检查依赖项"""
    print("检查系统依赖...")
    
    missing_deps = []
    
    try:
        import yaml
    except ImportError:
        missing_deps.append("PyYAML")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        from bs4 import BeautifulSoup
    except ImportError:
        missing_deps.append("beautifulsoup4")
    
    if missing_deps:
        print("❌ 缺少以下依赖项:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖项已安装")
    return True

def get_user_input():
    """获取用户输入"""
    print("\n请选择操作模式:")
    print("1. 快速搜索 (搜索并列出视频)")
    print("2. 完整处理 (搜索、下载、重命名、刮削)")
    print("3. 按时间筛选 (指定年月)")
    print("4. 自定义搜索")
    print("5. 系统测试")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选项 (0-5): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5']:
                return choice
            else:
                print("无效选项，请重新输入")
        except KeyboardInterrupt:
            print("\n\n用户取消操作")
            return '0'

def quick_search():
    """快速搜索模式"""
    print("\n=== 快速搜索模式 ===")
    
    query = input("请输入搜索关键词 (留空搜索全部): ").strip()
    
    try:
        pages = int(input("搜索页数 (默认1): ").strip() or "1")
    except ValueError:
        pages = 1
    
    cmd = f"python main.py --list-only --query \"{query}\" --pages {pages}"
    print(f"\n执行命令: {cmd}")
    print("-" * 40)
    
    os.system(cmd)

def full_process():
    """完整处理模式"""
    print("\n=== 完整处理模式 ===")
    
    query = input("请输入搜索关键词 (留空搜索全部): ").strip()
    
    try:
        pages = int(input("搜索页数 (默认1): ").strip() or "1")
    except ValueError:
        pages = 1
    
    print("\n处理选项:")
    download = input("是否下载视频? (Y/n): ").strip().lower() != 'n'
    rename = input("是否重命名文件? (Y/n): ").strip().lower() != 'n'
    scrape = input("是否刮削元数据? (Y/n): ").strip().lower() != 'n'
    
    cmd_parts = [f"python main.py --query \"{query}\" --pages {pages}"]
    
    if not download:
        cmd_parts.append("--no-download")
    if not rename:
        cmd_parts.append("--no-rename")
    if not scrape:
        cmd_parts.append("--no-scrape")
    
    cmd = " ".join(cmd_parts)
    print(f"\n执行命令: {cmd}")
    print("-" * 40)
    
    os.system(cmd)

def time_filter():
    """按时间筛选模式"""
    print("\n=== 按时间筛选模式 ===")
    
    try:
        year = int(input("请输入年份 (如2025): ").strip())
    except ValueError:
        print("无效年份")
        return
    
    try:
        month = int(input("请输入月份 (1-12, 0表示全年): ").strip() or "0")
    except ValueError:
        month = 0
    
    try:
        pages = int(input("搜索页数 (默认2): ").strip() or "2")
    except ValueError:
        pages = 2
    
    cmd = f"python main.py --year {year} --month {month} --pages {pages}"
    print(f"\n执行命令: {cmd}")
    print("-" * 40)
    
    os.system(cmd)

def custom_search():
    """自定义搜索模式"""
    print("\n=== 自定义搜索模式 ===")
    
    query = input("搜索关键词: ").strip()
    genre = input("视频类型 (默认'裏番'): ").strip() or "裏番"
    
    try:
        year = int(input("年份 (0表示不限制): ").strip() or "0")
    except ValueError:
        year = 0
    
    try:
        month = int(input("月份 (0表示不限制): ").strip() or "0")
    except ValueError:
        month = 0
    
    try:
        pages = int(input("搜索页数 (默认1): ").strip() or "1")
    except ValueError:
        pages = 1
    
    verbose = input("详细输出? (y/N): ").strip().lower() == 'y'
    
    cmd_parts = [
        f"python main.py",
        f"--query \"{query}\"",
        f"--genre \"{genre}\"",
        f"--pages {pages}"
    ]
    
    if year > 0:
        cmd_parts.append(f"--year {year}")
    if month > 0:
        cmd_parts.append(f"--month {month}")
    if verbose:
        cmd_parts.append("--verbose")
    
    cmd = " ".join(cmd_parts)
    print(f"\n执行命令: {cmd}")
    print("-" * 40)
    
    os.system(cmd)

def run_tests():
    """运行系统测试"""
    print("\n=== 系统测试 ===")
    print("运行系统测试...")
    print("-" * 40)
    
    os.system("python test_system.py")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖项
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 检查配置文件
    if not Path("config.yml").exists():
        print("❌ 配置文件 config.yml 不存在")
        input("\n按回车键退出...")
        return
    
    print("✅ 配置文件存在")
    
    while True:
        choice = get_user_input()
        
        if choice == '0':
            print("\n再见！")
            break
        elif choice == '1':
            quick_search()
        elif choice == '2':
            full_process()
        elif choice == '3':
            time_filter()
        elif choice == '4':
            custom_search()
        elif choice == '5':
            run_tests()
        
        print("\n" + "=" * 60)
        input("按回车键继续...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序异常: {e}")
        input("按回车键退出...")
        sys.exit(1)
