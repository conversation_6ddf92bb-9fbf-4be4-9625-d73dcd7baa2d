#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 视频下载和处理主程序
集成抓取、下载、重命名、刮削功能
"""

import os
import sys
import yaml
import logging
import argparse
import asyncio
import aiohttp
import aiofiles
from pathlib import Path
from typing import List, Dict, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from urllib.parse import urlparse
import time
from dataclasses import asdict

# 导入自定义模块
from craw import HanimeCrawler, VideoInfo
from rename import FileRenamer
from scape import VideoScraper


class DownloadManager:
    """视频下载管理器"""

    def __init__(self, config_path: str = "config.yml"):
        """初始化下载管理器"""
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        self.download_dir = Path(self.config.get('download', {}).get('download_dir', './downloads'))
        self.temp_dir = Path(self.config.get('download', {}).get('temp_dir', './temp'))

        # 创建目录
        self.download_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 下载统计
        self.download_stats = {
            'total': 0,
            'completed': 0,
            'failed': 0,
            'skipped': 0
        }

        # 线程锁
        self.stats_lock = threading.Lock()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'download': {
                'download_dir': './downloads',
                'temp_dir': './temp',
                'settings': {
                    'max_concurrent': 3,
                    'chunk_size': 1024,
                    'timeout': 300,
                    'retry_times': 3,
                    'resume_download': True
                }
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('DownloadManager')

        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        logger.setLevel(level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s'))
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def download_video(self, video_info: VideoInfo, custom_filename: str = None) -> bool:
        """下载单个视频"""
        if not video_info.video_url:
            self.logger.warning(f"视频 {video_info.title} 没有下载链接")
            return False

        # 确定文件名
        if custom_filename:
            filename = custom_filename
        else:
            filename = self._generate_filename(video_info)

        file_path = self.download_dir / filename
        temp_path = self.temp_dir / f"{filename}.tmp"

        # 检查文件是否已存在
        if file_path.exists():
            self.logger.info(f"文件已存在，跳过下载: {filename}")
            with self.stats_lock:
                self.download_stats['skipped'] += 1
            return True

        self.logger.info(f"开始下载: {video_info.title}")
        self.logger.debug(f"下载URL: {video_info.video_url}")
        self.logger.debug(f"保存路径: {file_path}")

        try:
            success = self._download_file_with_resume(video_info.video_url, file_path, temp_path)

            if success:
                self.logger.info(f"下载完成: {filename}")
                with self.stats_lock:
                    self.download_stats['completed'] += 1
                return True
            else:
                self.logger.error(f"下载失败: {filename}")
                with self.stats_lock:
                    self.download_stats['failed'] += 1
                return False

        except Exception as e:
            self.logger.error(f"下载异常: {filename} - {e}")
            with self.stats_lock:
                self.download_stats['failed'] += 1
            return False

    def _generate_filename(self, video_info: VideoInfo) -> str:
        """生成文件名"""
        # 从URL中提取文件扩展名
        parsed_url = urlparse(video_info.video_url)
        path = parsed_url.path
        ext = os.path.splitext(path)[1] or '.mp4'

        # 清理标题作为文件名
        safe_title = self._sanitize_filename(video_info.title)

        # 如果有视频ID，添加到文件名中
        if video_info.id:
            filename = f"{safe_title}_{video_info.id}{ext}"
        else:
            filename = f"{safe_title}{ext}"

        return filename

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名中的非法字符"""
        # 替换非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            filename = filename.replace(char, '_')

        # 移除多余的空格和点
        filename = filename.strip(' .')

        # 限制长度
        max_length = self.config.get('rename', {}).get('max_filename_length', 200)
        if len(filename) > max_length:
            filename = filename[:max_length]

        return filename

    def _download_file_with_resume(self, url: str, file_path: Path, temp_path: Path) -> bool:
        """支持断点续传的文件下载"""
        settings = self.config.get('download', {}).get('settings', {})
        chunk_size = settings.get('chunk_size', 1024) * 1024  # 转换为字节
        timeout = settings.get('timeout', 300)
        retry_times = settings.get('retry_times', 3)
        resume_download = settings.get('resume_download', True)

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        # 检查是否支持断点续传
        resume_pos = 0
        if resume_download and temp_path.exists():
            resume_pos = temp_path.stat().st_size
            headers['Range'] = f'bytes={resume_pos}-'
            self.logger.info(f"断点续传，从位置 {resume_pos} 开始")

        for attempt in range(retry_times):
            try:
                import requests

                response = requests.get(url, headers=headers, stream=True, timeout=timeout)

                # 检查响应状态
                if response.status_code not in [200, 206]:
                    self.logger.warning(f"HTTP状态码: {response.status_code}")
                    if attempt < retry_times - 1:
                        continue
                    return False

                # 获取文件总大小
                content_length = response.headers.get('content-length')
                if content_length:
                    total_size = int(content_length)
                    if resume_pos > 0:
                        total_size += resume_pos
                else:
                    total_size = 0

                # 下载文件
                mode = 'ab' if resume_pos > 0 else 'wb'
                downloaded = resume_pos

                with open(temp_path, mode) as f:
                    for chunk in response.iter_content(chunk_size=chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded += len(chunk)

                            # 显示进度
                            if total_size > 0:
                                progress = (downloaded / total_size) * 100
                                self.logger.debug(f"下载进度: {progress:.1f}% ({downloaded}/{total_size})")

                # 下载完成，移动到最终位置
                temp_path.rename(file_path)
                return True

            except Exception as e:
                self.logger.warning(f"下载尝试 {attempt + 1} 失败: {e}")
                if attempt < retry_times - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    # 清理临时文件
                    if temp_path.exists():
                        temp_path.unlink()
                    return False

        return False

    def download_videos_batch(self, videos: List[VideoInfo]) -> Dict[str, int]:
        """批量下载视频"""
        if not videos:
            self.logger.warning("没有视频需要下载")
            return self.download_stats

        # 重置统计
        with self.stats_lock:
            self.download_stats = {
                'total': len(videos),
                'completed': 0,
                'failed': 0,
                'skipped': 0
            }

        max_concurrent = self.config.get('download', {}).get('settings', {}).get('max_concurrent', 3)

        self.logger.info(f"开始批量下载 {len(videos)} 个视频，最大并发数: {max_concurrent}")

        # 使用线程池进行并发下载
        with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            # 提交所有下载任务
            future_to_video = {
                executor.submit(self.download_video, video): video
                for video in videos
            }

            # 处理完成的任务
            for future in as_completed(future_to_video):
                video = future_to_video[future]
                try:
                    success = future.result()
                    if success:
                        self.logger.info(f"✓ 下载成功: {video.title}")
                    else:
                        self.logger.error(f"✗ 下载失败: {video.title}")
                except Exception as e:
                    self.logger.error(f"✗ 下载异常: {video.title} - {e}")
                    with self.stats_lock:
                        self.download_stats['failed'] += 1

        # 输出统计信息
        self._print_download_stats()
        return self.download_stats

    def _print_download_stats(self):
        """打印下载统计信息"""
        stats = self.download_stats
        total = stats['total']
        completed = stats['completed']
        failed = stats['failed']
        skipped = stats['skipped']

        self.logger.info("=" * 50)
        self.logger.info("下载统计:")
        self.logger.info(f"总数: {total}")
        self.logger.info(f"成功: {completed}")
        self.logger.info(f"失败: {failed}")
        self.logger.info(f"跳过: {skipped}")
        self.logger.info(f"成功率: {(completed / total * 100):.1f}%" if total > 0 else "0%")
        self.logger.info("=" * 50)


class VideoProcessor:
    """视频处理器 - 集成所有功能"""

    def __init__(self, config_path: str = "config.yml"):
        """初始化视频处理器"""
        self.config_path = config_path
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()

        # 初始化各个组件
        self.crawler = HanimeCrawler(config_path)
        self.downloader = DownloadManager(config_path)
        self.renamer = FileRenamer(config_path)
        self.scraper = VideoScraper(config_path)

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {}

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('VideoProcessor')

        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        logger.setLevel(level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s'))
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def process_videos(self,
                      query: str = "",
                      genre: str = "裏番",
                      year: int = 0,
                      month: int = 0,
                      max_pages: int = 1,
                      download: bool = True,
                      rename: bool = True,
                      scrape: bool = True) -> List[VideoInfo]:
        """完整的视频处理流程"""

        self.logger.info("开始视频处理流程")
        self.logger.info(f"搜索参数: 查询='{query}', 类型='{genre}', 年份={year}, 月份={month}")

        # 1. 搜索视频
        self.logger.info("步骤 1: 搜索视频...")
        videos = self.crawler.search_videos(
            query=query,
            genre=genre,
            year=year,
            month=month,
            max_pages=max_pages
        )

        if not videos:
            self.logger.warning("没有找到视频")
            return []

        self.logger.info(f"找到 {len(videos)} 个视频")

        # 2. 获取详细信息
        self.logger.info("步骤 2: 获取视频详细信息...")
        detailed_videos = []
        for i, video in enumerate(videos, 1):
            self.logger.info(f"获取详情 ({i}/{len(videos)}): {video.title}")
            detailed_video = self.crawler.get_video_details(video)
            detailed_videos.append(detailed_video)

        # 3. 下载视频
        if download:
            self.logger.info("步骤 3: 下载视频...")
            download_stats = self.downloader.download_videos_batch(detailed_videos)

            # 只处理成功下载的视频
            if download_stats['completed'] == 0:
                self.logger.warning("没有成功下载的视频")
                return detailed_videos

        # 4. 重命名文件
        if rename:
            self.logger.info("步骤 4: 重命名文件...")
            for video in detailed_videos:
                try:
                    self.renamer.rename_video_file(video)
                except Exception as e:
                    self.logger.error(f"重命名失败: {video.title} - {e}")

        # 5. 刮削元数据
        if scrape:
            self.logger.info("步骤 5: 刮削元数据...")
            for video in detailed_videos:
                try:
                    self.scraper.scrape_and_save_nfo(video)
                except Exception as e:
                    self.logger.error(f"刮削失败: {video.title} - {e}")

        self.logger.info("视频处理流程完成")
        return detailed_videos


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Hanime1.me 视频下载和处理工具')

    # 搜索参数
    parser.add_argument('--query', '-q', default='', help='搜索关键词')
    parser.add_argument('--genre', '-g', default='裏番', help='视频类型')
    parser.add_argument('--year', '-y', type=int, default=0, help='年份筛选')
    parser.add_argument('--month', '-m', type=int, default=0, help='月份筛选')
    parser.add_argument('--pages', '-p', type=int, default=1, help='抓取页数')

    # 处理选项
    parser.add_argument('--no-download', action='store_true', help='不下载视频')
    parser.add_argument('--no-rename', action='store_true', help='不重命名文件')
    parser.add_argument('--no-scrape', action='store_true', help='不刮削元数据')

    # 其他选项
    parser.add_argument('--config', '-c', default='config.yml', help='配置文件路径')
    parser.add_argument('--list-only', action='store_true', help='只列出视频，不进行处理')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 创建视频处理器
        processor = VideoProcessor(args.config)

        if args.list_only:
            # 只搜索和列出视频
            crawler = HanimeCrawler(args.config)
            videos = crawler.search_videos(
                query=args.query,
                genre=args.genre,
                year=args.year,
                month=args.month,
                max_pages=args.pages
            )

            print(f"\n找到 {len(videos)} 个视频:")
            print("-" * 80)

            for i, video in enumerate(videos, 1):
                print(f"{i}. {video.title}")
                print(f"   ID: {video.id}")
                print(f"   URL: {video.url}")
                print(f"   时长: {video.duration}")
                print(f"   评分: {video.rating}")
                print()
        else:
            # 完整处理流程
            videos = processor.process_videos(
                query=args.query,
                genre=args.genre,
                year=args.year,
                month=args.month,
                max_pages=args.pages,
                download=not args.no_download,
                rename=not args.no_rename,
                scrape=not args.no_scrape
            )

            print(f"\n处理完成，共处理 {len(videos)} 个视频")

    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()