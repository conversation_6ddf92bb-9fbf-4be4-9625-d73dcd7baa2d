#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
用于测试各个模块的基本功能
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import yaml
        print("✓ yaml 导入成功")
    except ImportError as e:
        print(f"✗ yaml 导入失败: {e}")
        return False
    
    try:
        import requests
        print("✓ requests 导入成功")
    except ImportError as e:
        print(f"✗ requests 导入失败: {e}")
        return False
    
    try:
        from bs4 import BeautifulSoup
        print("✓ beautifulsoup4 导入成功")
    except ImportError as e:
        print(f"✗ beautifulsoup4 导入失败: {e}")
        print("请运行: pip install beautifulsoup4")
        return False
    
    return True

def test_config():
    """测试配置文件"""
    print("\n测试配置文件...")
    
    config_path = Path("config.yml")
    if not config_path.exists():
        print("✗ config.yml 文件不存在")
        return False
    
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必要的配置项
        required_sections = ['crawl', 'download', 'rename', 'scraping']
        for section in required_sections:
            if section not in config:
                print(f"✗ 配置文件缺少 {section} 部分")
                return False
            else:
                print(f"✓ 配置部分 {section} 存在")
        
        print("✓ 配置文件格式正确")
        return True
        
    except Exception as e:
        print(f"✗ 配置文件解析失败: {e}")
        return False

def test_modules():
    """测试自定义模块"""
    print("\n测试自定义模块...")
    
    modules = ['craw', 'rename', 'scape', 'main']
    
    for module_name in modules:
        try:
            if module_name == 'craw':
                from craw import HanimeCrawler, VideoInfo
                print(f"✓ {module_name} 模块导入成功")
            elif module_name == 'rename':
                from rename import FileRenamer
                print(f"✓ {module_name} 模块导入成功")
            elif module_name == 'scape':
                from scape import VideoScraper
                print(f"✓ {module_name} 模块导入成功")
            elif module_name == 'main':
                from main import DownloadManager, VideoProcessor
                print(f"✓ {module_name} 模块导入成功")
                
        except ImportError as e:
            print(f"✗ {module_name} 模块导入失败: {e}")
            return False
        except Exception as e:
            print(f"✗ {module_name} 模块错误: {e}")
            return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        # 测试VideoInfo数据类
        from craw import VideoInfo
        video = VideoInfo(id="test", title="测试视频")
        print("✓ VideoInfo 数据类工作正常")
        
        # 测试配置加载
        from main import VideoProcessor
        processor = VideoProcessor()
        print("✓ VideoProcessor 初始化成功")
        
        # 测试目录创建
        download_dir = Path("./downloads")
        temp_dir = Path("./temp")
        
        download_dir.mkdir(exist_ok=True)
        temp_dir.mkdir(exist_ok=True)
        
        print("✓ 目录创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_network():
    """测试网络连接"""
    print("\n测试网络连接...")
    
    try:
        import requests
        
        # 测试基本网络连接
        response = requests.get("https://httpbin.org/get", timeout=10)
        if response.status_code == 200:
            print("✓ 网络连接正常")
        else:
            print(f"✗ 网络连接异常: {response.status_code}")
            return False
        
        # 测试目标网站连接（可选）
        try:
            response = requests.get("https://hanime1.me", timeout=10)
            print("✓ 目标网站可访问")
        except Exception as e:
            print(f"⚠ 目标网站访问可能有问题: {e}")
            print("  这可能是正常的，取决于网络环境")
        
        return True
        
    except Exception as e:
        print(f"✗ 网络测试失败: {e}")
        return False

def create_sample_files():
    """创建示例文件"""
    print("\n创建示例文件...")
    
    try:
        # 创建示例视频文件（空文件）
        download_dir = Path("./downloads")
        download_dir.mkdir(exist_ok=True)
        
        sample_video = download_dir / "sample_video_12345.mp4"
        if not sample_video.exists():
            sample_video.touch()
            print("✓ 创建示例视频文件")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建示例文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("Hanime1.me 视频处理系统 - 系统测试")
    print("=" * 60)
    
    tests = [
        ("模块导入", test_imports),
        ("配置文件", test_config),
        ("自定义模块", test_modules),
        ("基本功能", test_basic_functionality),
        ("网络连接", test_network),
        ("示例文件", create_sample_files)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        print("\n使用方法:")
        print("  python main.py --help")
        print("  python main.py --list-only --year 2025 --month 1")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
        print("\n常见解决方案:")
        print("  1. 安装依赖: pip install -r requirements.txt")
        print("  2. 检查网络连接")
        print("  3. 确保配置文件正确")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
