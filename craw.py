#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hanime1.me 网站抓取模块
用于抓取视频列表和详细信息
"""

import requests
import time
import re
import yaml
import logging
from urllib.parse import urljoin, urlparse, parse_qs
from bs4 import BeautifulSoup
from typing import List, Dict, Optional, Any
import json
from dataclasses import dataclass
from datetime import datetime


@dataclass
class VideoInfo:
    """视频信息数据类"""
    id: str
    title: str
    title_jp: str = ""
    title_en: str = ""
    url: str = ""
    thumbnail: str = ""
    duration: str = ""
    release_date: str = ""
    studio: str = ""
    tags: List[str] = None
    rating: float = 0.0
    views: int = 0
    description: str = ""
    video_url: str = ""
    cover_image: str = ""

    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class HanimeCrawler:
    """Hanime1.me 网站爬虫类"""

    def __init__(self, config_path: str = "config.yml"):
        """初始化爬虫"""
        self.config = self._load_config(config_path)
        self.session = requests.Session()
        self._setup_session()
        self.logger = self._setup_logger()

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'crawl': {
                'base_url': 'https://hanime1.me',
                'search_url': 'https://hanime1.me/search',
                'settings': {
                    'max_pages': 10,
                    'delay_between_requests': 2,
                    'timeout': 30,
                    'retry_times': 3,
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                },
                'proxy': {'enabled': False}
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }

    def _setup_session(self):
        """设置请求会话"""
        crawl_config = self.config.get('crawl', {})
        settings = crawl_config.get('settings', {})

        # 设置请求头
        self.session.headers.update({
            'User-Agent': settings.get('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        # 设置代理
        proxy_config = crawl_config.get('proxy', {})
        if proxy_config.get('enabled', False):
            proxies = {}
            if proxy_config.get('http'):
                proxies['http'] = proxy_config['http']
            if proxy_config.get('https'):
                proxies['https'] = proxy_config['https']
            if proxies:
                self.session.proxies.update(proxies)

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('HanimeCrawler')

        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        logger.setLevel(level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s'))
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _make_request(self, url: str, params: Dict = None, retries: int = None) -> Optional[requests.Response]:
        """发送HTTP请求"""
        if retries is None:
            retries = self.config.get('crawl', {}).get('settings', {}).get('retry_times', 3)

        timeout = self.config.get('crawl', {}).get('settings', {}).get('timeout', 30)
        delay = self.config.get('crawl', {}).get('settings', {}).get('delay_between_requests', 2)

        for attempt in range(retries + 1):
            try:
                self.logger.debug(f"请求URL: {url}, 参数: {params}, 尝试: {attempt + 1}")
                response = self.session.get(url, params=params, timeout=timeout)
                response.raise_for_status()

                # 请求间隔
                if delay > 0:
                    time.sleep(delay)

                return response

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"请求失败 (尝试 {attempt + 1}/{retries + 1}): {e}")
                if attempt < retries:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    self.logger.error(f"请求最终失败: {url}")
                    return None

    def search_videos(self,
                     query: str = "",
                     genre: str = "裏番",
                     year: int = 0,
                     month: int = 0,
                     max_pages: int = None) -> List[VideoInfo]:
        """搜索视频"""
        if max_pages is None:
            max_pages = self.config.get('crawl', {}).get('settings', {}).get('max_pages', 10)

        base_url = self.config.get('crawl', {}).get('search_url', 'https://hanime1.me/search')

        # 构建搜索参数
        params = {
            'query': query,
            'genre': genre,
            'type': '',
            'sort': ''
        }

        # 添加时间筛选
        if year > 0:
            params['year'] = year
        if month > 0:
            params['month'] = month

        videos = []
        page = 1

        self.logger.info(f"开始搜索视频: 查询='{query}', 类型='{genre}', 年份={year}, 月份={month}")

        while page <= max_pages:
            self.logger.info(f"抓取第 {page} 页...")

            # 添加页码参数
            current_params = params.copy()
            if page > 1:
                current_params['page'] = page

            response = self._make_request(base_url, current_params)
            if not response:
                break

            # 解析页面
            page_videos = self._parse_search_page(response.text)
            if not page_videos:
                self.logger.info("没有找到更多视频，停止抓取")
                break

            videos.extend(page_videos)
            self.logger.info(f"第 {page} 页找到 {len(page_videos)} 个视频")

            page += 1

        self.logger.info(f"搜索完成，总共找到 {len(videos)} 个视频")
        return videos

    def _parse_search_page(self, html: str) -> List[VideoInfo]:
        """解析搜索结果页面"""
        videos = []

        try:
            soup = BeautifulSoup(html, 'html.parser')

            # 查找视频卡片容器
            video_cards = soup.find_all('div', class_='home-rows-videos-wrapper')
            if not video_cards:
                # 尝试其他可能的选择器
                video_cards = soup.find_all('div', class_='video-card')
                if not video_cards:
                    video_cards = soup.find_all('a', href=re.compile(r'/watch\?v='))

            for card in video_cards:
                try:
                    video_info = self._extract_video_info_from_card(card)
                    if video_info:
                        videos.append(video_info)
                except Exception as e:
                    self.logger.warning(f"解析视频卡片失败: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"解析搜索页面失败: {e}")

        return videos

    def _extract_video_info_from_card(self, card) -> Optional[VideoInfo]:
        """从视频卡片中提取信息"""
        try:
            # 提取视频链接和ID
            link_elem = card.find('a', href=re.compile(r'/watch\?v='))
            if not link_elem:
                return None

            video_url = link_elem.get('href', '')
            if not video_url.startswith('http'):
                base_url = self.config.get('crawl', {}).get('base_url', 'https://hanime1.me')
                video_url = urljoin(base_url, video_url)

            # 提取视频ID
            video_id = self._extract_video_id_from_url(video_url)
            if not video_id:
                return None

            # 提取标题
            title_elem = card.find('div', class_='home-rows-videos-title')
            if not title_elem:
                title_elem = card.find('h3') or card.find('h4') or card.find('span', class_='title')

            title = title_elem.get_text(strip=True) if title_elem else f"Video {video_id}"

            # 提取缩略图
            img_elem = card.find('img')
            thumbnail = img_elem.get('src', '') if img_elem else ''
            if thumbnail and not thumbnail.startswith('http'):
                base_url = self.config.get('crawl', {}).get('base_url', 'https://hanime1.me')
                thumbnail = urljoin(base_url, thumbnail)

            # 提取时长
            duration_elem = card.find('span', class_='duration') or card.find('div', class_='duration')
            duration = duration_elem.get_text(strip=True) if duration_elem else ""

            # 提取评分
            rating_elem = card.find('span', class_='rating') or card.find('div', class_='rating')
            rating = 0.0
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    rating = float(rating_match.group(1))

            # 提取观看次数
            views_elem = card.find('span', class_='views') or card.find('div', class_='views')
            views = 0
            if views_elem:
                views_text = views_elem.get_text(strip=True)
                views_match = re.search(r'(\d+)', views_text.replace(',', ''))
                if views_match:
                    views = int(views_match.group(1))

            return VideoInfo(
                id=video_id,
                title=title,
                url=video_url,
                thumbnail=thumbnail,
                duration=duration,
                rating=rating,
                views=views
            )

        except Exception as e:
            self.logger.warning(f"提取视频信息失败: {e}")
            return None

    def _extract_video_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取视频ID"""
        try:
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)

            # 从 ?v= 参数中获取ID
            if 'v' in query_params:
                return query_params['v'][0]

            # 从路径中提取ID
            path_match = re.search(r'/watch/(\d+)', parsed.path)
            if path_match:
                return path_match.group(1)

            return None

        except Exception:
            return None

    def get_video_details(self, video_info: VideoInfo) -> VideoInfo:
        """获取视频详细信息"""
        if not video_info.url:
            self.logger.warning(f"视频 {video_info.id} 没有URL")
            return video_info

        self.logger.info(f"获取视频详情: {video_info.title} ({video_info.id})")

        response = self._make_request(video_info.url)
        if not response:
            return video_info

        try:
            soup = BeautifulSoup(response.text, 'html.parser')

            # 更新视频信息
            self._extract_detailed_info(soup, video_info)

        except Exception as e:
            self.logger.error(f"解析视频详情页面失败: {e}")

        return video_info

    def _extract_detailed_info(self, soup: BeautifulSoup, video_info: VideoInfo):
        """从详情页面提取详细信息"""
        try:
            # 提取日文标题
            jp_title_elem = soup.find('h1', class_='video-title-jp') or soup.find('span', class_='jp-title')
            if jp_title_elem:
                video_info.title_jp = jp_title_elem.get_text(strip=True)

            # 提取英文标题
            en_title_elem = soup.find('h2', class_='video-title-en') or soup.find('span', class_='en-title')
            if en_title_elem:
                video_info.title_en = en_title_elem.get_text(strip=True)

            # 提取发布日期
            date_elem = soup.find('span', class_='release-date') or soup.find('div', class_='date')
            if date_elem:
                date_text = date_elem.get_text(strip=True)
                # 尝试解析日期格式
                date_match = re.search(r'(\d{4})-(\d{1,2})-(\d{1,2})', date_text)
                if date_match:
                    video_info.release_date = f"{date_match.group(1)}-{date_match.group(2):0>2}-{date_match.group(3):0>2}"

            # 提取制作商
            studio_elem = soup.find('span', class_='studio') or soup.find('div', class_='brand')
            if studio_elem:
                video_info.studio = studio_elem.get_text(strip=True)

            # 提取标签
            tag_elems = soup.find_all('a', class_='tag') or soup.find_all('span', class_='genre')
            if tag_elems:
                video_info.tags = [tag.get_text(strip=True) for tag in tag_elems]

            # 提取描述
            desc_elem = soup.find('div', class_='description') or soup.find('p', class_='synopsis')
            if desc_elem:
                video_info.description = desc_elem.get_text(strip=True)

            # 提取视频播放链接
            video_elem = soup.find('video') or soup.find('source')
            if video_elem:
                video_src = video_elem.get('src') or video_elem.get('data-src')
                if video_src:
                    if not video_src.startswith('http'):
                        base_url = self.config.get('crawl', {}).get('base_url', 'https://hanime1.me')
                        video_src = urljoin(base_url, video_src)
                    video_info.video_url = video_src

            # 提取封面图片
            cover_elem = soup.find('img', class_='cover') or soup.find('div', class_='poster')
            if cover_elem:
                if cover_elem.name == 'img':
                    cover_src = cover_elem.get('src') or cover_elem.get('data-src')
                else:
                    cover_img = cover_elem.find('img')
                    cover_src = cover_img.get('src') or cover_img.get('data-src') if cover_img else None

                if cover_src:
                    if not cover_src.startswith('http'):
                        base_url = self.config.get('crawl', {}).get('base_url', 'https://hanime1.me')
                        cover_src = urljoin(base_url, cover_src)
                    video_info.cover_image = cover_src

        except Exception as e:
            self.logger.warning(f"提取详细信息失败: {e}")


def main():
    """主函数，用于测试"""
    import argparse

    parser = argparse.ArgumentParser(description='Hanime1.me 视频抓取工具')
    parser.add_argument('--query', '-q', default='', help='搜索关键词')
    parser.add_argument('--genre', '-g', default='裏番', help='视频类型')
    parser.add_argument('--year', '-y', type=int, default=0, help='年份筛选')
    parser.add_argument('--month', '-m', type=int, default=0, help='月份筛选')
    parser.add_argument('--pages', '-p', type=int, default=1, help='抓取页数')
    parser.add_argument('--details', '-d', action='store_true', help='获取详细信息')
    parser.add_argument('--config', '-c', default='config.yml', help='配置文件路径')

    args = parser.parse_args()

    # 创建爬虫实例
    crawler = HanimeCrawler(args.config)

    # 搜索视频
    videos = crawler.search_videos(
        query=args.query,
        genre=args.genre,
        year=args.year,
        month=args.month,
        max_pages=args.pages
    )

    print(f"\n找到 {len(videos)} 个视频:")
    print("-" * 80)

    for i, video in enumerate(videos, 1):
        print(f"{i}. {video.title}")
        print(f"   ID: {video.id}")
        print(f"   URL: {video.url}")
        print(f"   时长: {video.duration}")
        print(f"   评分: {video.rating}")
        print(f"   观看: {video.views}")

        if args.details:
            print("   获取详细信息...")
            detailed_video = crawler.get_video_details(video)
            if detailed_video.title_jp:
                print(f"   日文标题: {detailed_video.title_jp}")
            if detailed_video.studio:
                print(f"   制作商: {detailed_video.studio}")
            if detailed_video.release_date:
                print(f"   发布日期: {detailed_video.release_date}")
            if detailed_video.tags:
                print(f"   标签: {', '.join(detailed_video.tags)}")

        print()


if __name__ == '__main__':
    main()