# Hanime1.me 视频抓取和处理系统配置文件

# 网站抓取配置
crawl:
  # 基础URL配置
  base_url: "https://hanime1.me"
  search_url: "https://hanime1.me/search"

  # 搜索参数
  search_params:
    genre: "裏番"  # 默认类型
    type: ""       # 视频类型
    sort: ""       # 排序方式
    query: ""      # 搜索关键词

  # 时间筛选（可选）
  date_filter:
    enabled: true
    year: 2025     # 年份，0表示不限制
    month: 1       # 月份，0表示不限制
    day: 0         # 日期，0表示不限制

  # 抓取设置
  settings:
    max_pages: 10          # 最大抓取页数
    delay_between_requests: 2  # 请求间隔（秒）
    timeout: 30            # 请求超时时间
    retry_times: 3         # 重试次数
    user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

  # 代理设置（可选）
  proxy:
    enabled: false
    http: ""
    https: ""

# 下载配置
download:
  # 下载目录
  download_dir: "./downloads"
  temp_dir: "./temp"

  # 下载设置
  settings:
    max_concurrent: 3      # 最大并发下载数
    chunk_size: 1024       # 下载块大小（KB）
    timeout: 300           # 下载超时时间（秒）
    retry_times: 3         # 重试次数
    resume_download: true  # 支持断点续传

  # 文件格式过滤
  file_formats:
    video: [".mp4", ".mkv", ".avi", ".mov", ".wmv"]
    subtitle: [".srt", ".ass", ".vtt"]
    image: [".jpg", ".jpeg", ".png", ".webp"]

# 重命名配置
rename:
  # 命名规则模板
  templates:
    # 默认模板：[制作商] 标题 [编号]
    default: "[{studio}] {title} [{num}]"
    # 带年份模板：[制作商] 标题 ({year}) [编号]
    with_year: "[{studio}] {title} ({year}) [{num}]"
    # 简单模板：标题 [编号]
    simple: "{title} [{num}]"

  # 当前使用的模板
  current_template: "default"

  # 字符替换规则
  char_replacements:
    "/": "／"
    "\\": "＼"
    ":": "："
    "*": "＊"
    "?": "？"
    "\"": """
    "<": "＜"
    ">": "＞"
    "|": "｜"

  # 长度限制
  max_filename_length: 200

  # 重复文件处理
  duplicate_handling: "skip"  # skip, overwrite, rename

# 刮削配置
scraping:
  # 数据源配置
  sources:
    hanime1:
      enabled: true
      base_url: "https://hanime1.me"
      priority: 1

    themoviedb:
      enabled: true
      base_url: "https://api.themoviedb.org/3"
      api_key: ""  # 需要申请API密钥
      priority: 2

    getchu:
      enabled: true
      base_url: "https://www.getchu.com"
      priority: 3

  # NFO文件配置
  nfo:
    encoding: "utf-8"
    template_file: ""  # 可选的NFO模板文件路径

    # 默认字段映射
    field_mapping:
      title: "title"
      originaltitle: "originaltitle"
      plot: "plot"
      outline: "outline"
      year: "year"
      premiered: "premiered"
      releasedate: "releasedate"
      runtime: "runtime"
      rating: "rating"
      studio: "studio"
      maker: "maker"
      num: "num"
      genre: "genre"
      tag: "tag"
      customrating: "customrating"
      mpaa: "mpaa"

  # 图片下载配置
  images:
    download_poster: true
    download_fanart: true
    download_thumb: true
    poster_size: "original"  # original, large, medium, small
    fanart_size: "original"

  # 刮削设置
  settings:
    delay_between_requests: 1  # 请求间隔（秒）
    timeout: 30               # 请求超时时间
    retry_times: 3            # 重试次数
    skip_existing_nfo: true   # 跳过已存在的NFO文件

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "scraper.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 通知配置（可选）
notifications:
  enabled: false

  # 邮件通知
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    from_email: ""
    to_emails: []

  # 企业微信通知
  wechat:
    enabled: false
    webhook_url: ""

  # 钉钉通知
  dingtalk:
    enabled: false
    webhook_url: ""
    secret: ""

# 数据库配置（可选，用于记录处理历史）
database:
  enabled: false
  type: "sqlite"  # sqlite, mysql, postgresql
  path: "scraper.db"  # SQLite数据库文件路径

  # MySQL/PostgreSQL配置
  host: "localhost"
  port: 3306
  username: ""
  password: ""
  database: ""

# 高级设置
advanced:
  # 并发控制
  max_workers: 5

  # 内存限制
  max_memory_usage: "1GB"

  # 临时文件清理
  cleanup_temp_files: true

  # 调试模式
  debug_mode: false

  # 干运行模式（只显示操作，不实际执行）
  dry_run: false