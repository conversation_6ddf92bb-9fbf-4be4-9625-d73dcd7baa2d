#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件重命名模块
根据视频信息自动重命名文件，支持自定义命名规则
"""

import os
import re
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import asdict
import shutil
from datetime import datetime

# 导入视频信息类
from craw import VideoInfo


class FileRenamer:
    """文件重命名器"""

    def __init__(self, config_path: str = "config.yml"):
        """初始化重命名器"""
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        self.download_dir = Path(self.config.get('download', {}).get('download_dir', './downloads'))

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'rename': {
                'templates': {
                    'default': '[{studio}] {title} [{num}]',
                    'with_year': '[{studio}] {title} ({year}) [{num}]',
                    'simple': '{title} [{num}]'
                },
                'current_template': 'default',
                'char_replacements': {
                    '/': '／',
                    '\\': '＼',
                    ':': '：',
                    '*': '＊',
                    '?': '？',
                    '"': '"',
                    '<': '＜',
                    '>': '＞',
                    '|': '｜'
                },
                'max_filename_length': 200,
                'duplicate_handling': 'skip'
            },
            'download': {
                'download_dir': './downloads'
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('FileRenamer')

        log_config = self.config.get('logging', {})
        level = getattr(logging, log_config.get('level', 'INFO').upper())
        logger.setLevel(level)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(log_config.get('format', '%(asctime)s - %(levelname)s - %(message)s'))
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def generate_filename(self, video_info: VideoInfo, template_name: str = None) -> str:
        """根据模板生成文件名"""
        rename_config = self.config.get('rename', {})
        templates = rename_config.get('templates', {})

        # 确定使用的模板
        if template_name and template_name in templates:
            template = templates[template_name]
        else:
            current_template = rename_config.get('current_template', 'default')
            template = templates.get(current_template, templates.get('default', '{title}'))

        # 准备替换变量
        variables = self._prepare_variables(video_info)

        # 应用模板
        try:
            filename = template.format(**variables)
        except KeyError as e:
            self.logger.warning(f"模板变量缺失: {e}, 使用默认模板")
            filename = templates.get('simple', '{title}').format(**variables)

        # 清理文件名
        filename = self._sanitize_filename(filename)

        return filename

    def _prepare_variables(self, video_info: VideoInfo) -> Dict[str, str]:
        """准备模板变量"""
        variables = {}

        # 基础信息
        variables['title'] = video_info.title or "Unknown Title"
        variables['title_jp'] = video_info.title_jp or video_info.title or "Unknown Title"
        variables['title_en'] = video_info.title_en or ""
        variables['id'] = video_info.id or ""
        variables['studio'] = video_info.studio or "Unknown Studio"
        variables['num'] = self._extract_number(video_info)

        # 日期信息
        if video_info.release_date:
            try:
                # 尝试解析日期
                if '-' in video_info.release_date:
                    date_parts = video_info.release_date.split('-')
                    variables['year'] = date_parts[0]
                    variables['month'] = date_parts[1] if len(date_parts) > 1 else ""
                    variables['day'] = date_parts[2] if len(date_parts) > 2 else ""
                else:
                    variables['year'] = video_info.release_date[:4] if len(video_info.release_date) >= 4 else ""
                    variables['month'] = ""
                    variables['day'] = ""
            except:
                variables['year'] = ""
                variables['month'] = ""
                variables['day'] = ""
        else:
            variables['year'] = ""
            variables['month'] = ""
            variables['day'] = ""

        # 其他信息
        variables['rating'] = str(video_info.rating) if video_info.rating else ""
        variables['duration'] = video_info.duration or ""
        variables['views'] = str(video_info.views) if video_info.views else ""

        # 标签信息
        if video_info.tags:
            variables['tags'] = '_'.join(video_info.tags[:3])  # 只取前3个标签
            variables['first_tag'] = video_info.tags[0] if video_info.tags else ""
        else:
            variables['tags'] = ""
            variables['first_tag'] = ""

        # 确保所有变量都是字符串
        for key, value in variables.items():
            if value is None:
                variables[key] = ""
            else:
                variables[key] = str(value)

        return variables

    def _extract_number(self, video_info: VideoInfo) -> str:
        """提取视频编号"""
        # 优先从ID中提取
        if video_info.id:
            return video_info.id

        # 从标题中提取编号
        title = video_info.title or ""

        # 查找常见的编号模式
        patterns = [
            r'\[([A-Z0-9\-]+)\]',  # [ABC-123] 格式
            r'([A-Z]{2,4}-\d{3,4})',  # ABC-123 格式
            r'(\d{4,6})',  # 纯数字编号
            r'([A-Z]+\d+)',  # 字母+数字
        ]

        for pattern in patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1)

        # 如果都没找到，返回视频ID或空字符串
        return video_info.id or ""

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名"""
        rename_config = self.config.get('rename', {})
        char_replacements = rename_config.get('char_replacements', {})
        max_length = rename_config.get('max_filename_length', 200)

        # 应用字符替换
        for old_char, new_char in char_replacements.items():
            filename = filename.replace(old_char, new_char)

        # 移除或替换其他非法字符
        illegal_chars = '<>:"/\\|?*'
        for char in illegal_chars:
            if char not in char_replacements:
                filename = filename.replace(char, '_')

        # 移除多余的空格和点
        filename = re.sub(r'\s+', ' ', filename)  # 多个空格替换为单个空格
        filename = filename.strip(' .')

        # 移除连续的下划线
        filename = re.sub(r'_+', '_', filename)

        # 限制长度
        if len(filename) > max_length:
            # 保留扩展名
            name_part = filename[:max_length]
            filename = name_part

        return filename

    def find_video_files(self, video_info: VideoInfo) -> List[Path]:
        """查找与视频相关的文件"""
        files = []

        # 支持的视频格式
        video_formats = self.config.get('download', {}).get('file_formats', {}).get('video', ['.mp4', '.mkv', '.avi'])

        # 搜索模式
        search_patterns = [
            f"*{video_info.id}*",  # 包含视频ID
            f"*{video_info.title[:20]}*",  # 包含标题前20个字符
        ]

        # 如果有原始文件名，也加入搜索
        if hasattr(video_info, 'original_filename') and video_info.original_filename:
            search_patterns.append(video_info.original_filename)

        for pattern in search_patterns:
            for file_path in self.download_dir.glob(pattern):
                if file_path.is_file() and file_path.suffix.lower() in video_formats:
                    files.append(file_path)

        # 去重
        unique_files = list(set(files))
        return unique_files

    def rename_video_file(self, video_info: VideoInfo, template_name: str = None) -> Optional[Path]:
        """重命名视频文件"""
        # 查找相关文件
        video_files = self.find_video_files(video_info)

        if not video_files:
            self.logger.warning(f"未找到视频文件: {video_info.title}")
            return None

        if len(video_files) > 1:
            self.logger.warning(f"找到多个视频文件: {video_info.title}")
            # 选择最大的文件（通常是主视频文件）
            video_file = max(video_files, key=lambda f: f.stat().st_size)
        else:
            video_file = video_files[0]

        self.logger.info(f"重命名文件: {video_file.name}")

        # 生成新文件名
        new_filename = self.generate_filename(video_info, template_name)

        # 保留原始扩展名
        original_ext = video_file.suffix
        if not new_filename.endswith(original_ext):
            new_filename += original_ext

        new_file_path = self.download_dir / new_filename

        # 检查重复文件处理
        if new_file_path.exists() and new_file_path != video_file:
            duplicate_handling = self.config.get('rename', {}).get('duplicate_handling', 'skip')

            if duplicate_handling == 'skip':
                self.logger.info(f"文件已存在，跳过重命名: {new_filename}")
                return video_file
            elif duplicate_handling == 'overwrite':
                self.logger.warning(f"覆盖现有文件: {new_filename}")
            elif duplicate_handling == 'rename':
                new_filename = self._generate_unique_filename(new_filename)
                new_file_path = self.download_dir / new_filename
                self.logger.info(f"生成唯一文件名: {new_filename}")

        # 执行重命名
        try:
            video_file.rename(new_file_path)
            self.logger.info(f"重命名成功: {video_file.name} -> {new_filename}")

            # 同时重命名相关文件（字幕、NFO等）
            self._rename_related_files(video_file, new_file_path)

            return new_file_path

        except Exception as e:
            self.logger.error(f"重命名失败: {e}")
            return None

    def _generate_unique_filename(self, filename: str) -> str:
        """生成唯一文件名"""
        base_name = Path(filename).stem
        extension = Path(filename).suffix
        counter = 1

        while True:
            new_filename = f"{base_name}_{counter:02d}{extension}"
            new_path = self.download_dir / new_filename

            if not new_path.exists():
                return new_filename

            counter += 1
            if counter > 99:  # 防止无限循环
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                return f"{base_name}_{timestamp}{extension}"

    def _rename_related_files(self, old_file_path: Path, new_file_path: Path):
        """重命名相关文件（字幕、NFO等）"""
        old_base_name = old_file_path.stem
        new_base_name = new_file_path.stem

        # 相关文件扩展名
        related_extensions = ['.nfo', '.srt', '.ass', '.vtt', '.jpg', '.jpeg', '.png', '.webp']

        for ext in related_extensions:
            old_related_file = old_file_path.parent / f"{old_base_name}{ext}"
            if old_related_file.exists():
                new_related_file = new_file_path.parent / f"{new_base_name}{ext}"
                try:
                    old_related_file.rename(new_related_file)
                    self.logger.debug(f"重命名相关文件: {old_related_file.name} -> {new_related_file.name}")
                except Exception as e:
                    self.logger.warning(f"重命名相关文件失败: {e}")

    def batch_rename(self, videos: List[VideoInfo], template_name: str = None) -> Dict[str, int]:
        """批量重命名文件"""
        stats = {
            'total': len(videos),
            'success': 0,
            'failed': 0,
            'skipped': 0
        }

        self.logger.info(f"开始批量重命名 {len(videos)} 个视频文件")

        for i, video in enumerate(videos, 1):
            self.logger.info(f"处理 ({i}/{len(videos)}): {video.title}")

            try:
                result = self.rename_video_file(video, template_name)
                if result:
                    stats['success'] += 1
                else:
                    stats['skipped'] += 1
            except Exception as e:
                self.logger.error(f"重命名失败: {video.title} - {e}")
                stats['failed'] += 1

        # 输出统计信息
        self.logger.info("=" * 50)
        self.logger.info("重命名统计:")
        self.logger.info(f"总数: {stats['total']}")
        self.logger.info(f"成功: {stats['success']}")
        self.logger.info(f"失败: {stats['failed']}")
        self.logger.info(f"跳过: {stats['skipped']}")
        self.logger.info("=" * 50)

        return stats

    def preview_rename(self, video_info: VideoInfo, template_name: str = None) -> Dict[str, str]:
        """预览重命名结果"""
        # 查找文件
        video_files = self.find_video_files(video_info)

        if not video_files:
            return {
                'status': 'error',
                'message': '未找到视频文件',
                'original': '',
                'new': ''
            }

        original_file = video_files[0] if len(video_files) == 1 else max(video_files, key=lambda f: f.stat().st_size)

        # 生成新文件名
        new_filename = self.generate_filename(video_info, template_name)

        # 保留扩展名
        if not new_filename.endswith(original_file.suffix):
            new_filename += original_file.suffix

        return {
            'status': 'success',
            'message': '预览成功',
            'original': original_file.name,
            'new': new_filename,
            'variables': self._prepare_variables(video_info)
        }


def main():
    """主函数，用于测试"""
    import argparse
    from craw import HanimeCrawler

    parser = argparse.ArgumentParser(description='文件重命名工具')
    parser.add_argument('--config', '-c', default='config.yml', help='配置文件路径')
    parser.add_argument('--template', '-t', help='使用的模板名称')
    parser.add_argument('--preview', '-p', action='store_true', help='预览模式，不实际重命名')
    parser.add_argument('--video-id', help='指定视频ID进行重命名')
    parser.add_argument('--search', '-s', help='搜索关键词')

    args = parser.parse_args()

    # 创建重命名器
    renamer = FileRenamer(args.config)

    if args.video_id:
        # 处理单个视频
        video_info = VideoInfo(id=args.video_id, title=f"Video {args.video_id}")

        if args.preview:
            result = renamer.preview_rename(video_info, args.template)
            print(f"原文件名: {result['original']}")
            print(f"新文件名: {result['new']}")
            print(f"状态: {result['message']}")
        else:
            result = renamer.rename_video_file(video_info, args.template)
            if result:
                print(f"重命名成功: {result.name}")
            else:
                print("重命名失败")

    elif args.search:
        # 搜索并重命名
        crawler = HanimeCrawler(args.config)
        videos = crawler.search_videos(query=args.search, max_pages=1)

        if videos:
            print(f"找到 {len(videos)} 个视频")

            if args.preview:
                for video in videos:
                    result = renamer.preview_rename(video, args.template)
                    print(f"视频: {video.title}")
                    print(f"  原文件名: {result.get('original', 'N/A')}")
                    print(f"  新文件名: {result.get('new', 'N/A')}")
                    print()
            else:
                stats = renamer.batch_rename(videos, args.template)
                print(f"重命名完成: 成功 {stats['success']}, 失败 {stats['failed']}, 跳过 {stats['skipped']}")
        else:
            print("没有找到视频")

    else:
        print("请指定 --video-id 或 --search 参数")


if __name__ == '__main__':
    main()